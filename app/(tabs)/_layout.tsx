import React from 'react';
import {useColorScheme} from '@/hooks/use-color-scheme';
import {Icon, Label, NativeTabs} from "expo-router/unstable-native-tabs";

export default function TabLayout() {
    const colorScheme = useColorScheme();

    return (
        <NativeTabs
            backgroundColor="black"
        >
            <NativeTabs.Trigger name="index">
                <NativeTabs.Trigger.TabBar
                    backgroundColor="white"
                />
                <Icon sf="house"/>
                <Label>Home</Label>
            </NativeTabs.Trigger>
            <NativeTabs.Trigger name="trainings">
                <NativeTabs.Trigger.TabBar
                    backgroundColor="white"
                />
                <Icon sf="list.bullet"/>
                <Label>Trainings</Label>
            </NativeTabs.Trigger>
            <NativeTabs.Trigger name="exercises">
                <NativeTabs.Trigger.TabBar
                    backgroundColor="white"
                />
                <Icon sf="dumbbell"/>
                <Label>Exercises</Label>
            </NativeTabs.Trigger>
        </NativeTabs>
    );
}
