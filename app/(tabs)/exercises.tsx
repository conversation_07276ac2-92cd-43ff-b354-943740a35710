import {ThemedText} from '@/components/themed-text';
import {ThemedView} from '@/components/themed-view';
import {Fonts} from '@/constants/theme';
import {useEffect, useState} from "react";
import {useDebounce} from "@uidotdev/usehooks";
import {FlatList, StyleSheet, TextInput} from 'react-native';
import {SafeAreaView} from "react-native-safe-area-context";
import {ThemedCard} from "@/components/themed-card";

const fetchExercises = async (page: number) => {
    const LIMIT = 25
    return fetch(`https://exercisesdb-sand.vercel.app/api/v1/exercises?limit=${LIMIT}&offset=${page * LIMIT}`)
        .then((response) => response.json())
}

const ExerciseCard = ({exercise}: {exercise: any}) => {
    return <ThemedCard style={{marginBottom: 8}}>
        <ThemedText style={{textTransform: "capitalize"}}>{exercise.name}</ThemedText>
    </ThemedCard>
}

export default function ExercisesScreen() {
    const [searchValue, setSearchValue] = useState("");
    const search = useDebounce(searchValue, 500)

    const [exercises, setExercises] = useState<any[]>([])


    useEffect(() => {
        fetchExercises(0).then(json => setExercises(json.data))
    }, []);

    return (
        <ThemedView style={{flex: 1}}>
            <SafeAreaView style={{padding: 16}}>
                <ThemedView style={styles.titleContainer}>
                    <ThemedText
                        type="title"
                        style={{
                            fontFamily: Fonts.rounded,
                        }}>
                        Exercises
                    </ThemedText>
                </ThemedView>
                <TextInput placeholder="Search" style={styles.input} onChangeText={(e) => setSearchValue(e)}/>
                <ThemedText>{search}</ThemedText>

                <FlatList
                    data={exercises}
                    renderItem={({item}) => <ExerciseCard exercise={item}/>}
                    keyExtractor={item => item.exerciseId}
                />
            </SafeAreaView>
        </ThemedView>
    );
}

const styles = StyleSheet.create({
    input: {
        color: "#FFF",
        borderColor: "#555",
        borderWidth: 1,
        borderStyle: "solid",
        padding: 8,
        borderRadius: 8
    },
    headerImage: {
        color: '#808080',
        bottom: -90,
        left: -35,
        position: 'absolute',
    },
    titleContainer: {
        flexDirection: 'row',
        gap: 8,
    },
});
