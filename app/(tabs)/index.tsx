import {Image} from 'expo-image';
import {StyleSheet, View} from 'react-native';

import {HelloWave} from '@/components/hello-wave';
import ParallaxScrollView from '@/components/parallax-scroll-view';
import {ThemedText} from '@/components/themed-text';
import {ThemedView} from '@/components/themed-view';
import {ThemedCard} from "@/components/themed-card";
import {Link} from "expo-router";
import {PlayButton} from "@/components/PlayButton";

export default function HomeScreen() {
    return (
        <ParallaxScrollView
            headerBackgroundColor={{light: '#A1CEDC', dark: '#1D3D47'}}
            headerImage={
                <Image
                    source={require('@/assets/images/partial-react-logo.png')}
                    style={styles.reactLogo}
                />
            }>
            <ThemedView style={styles.titleContainer}>
                <ThemedText type="title">Welcome!</ThemedText>
                <HelloWave/>
            </ThemedView>
            <ThemedCard style={styles.stepContainer}>
                <Link href="/training">
                    <View style={{width: "100%", display: "flex", justifyContent: "space-between", alignItems: "center", flexDirection: "row"}}>
                        <ThemedText type="subtitle">Today</ThemedText>
                        <View>
                            <PlayButton/>
                        </View>
                    </View>
                </Link>
            </ThemedCard>
        </ParallaxScrollView>
    );
}

const styles = StyleSheet.create({
    titleContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    stepContainer: {
        gap: 8,
        marginBottom: 8,
    },
    reactLogo: {
        height: 178,
        width: 290,
        bottom: 0,
        left: 0,
        position: 'absolute',
    },
});
