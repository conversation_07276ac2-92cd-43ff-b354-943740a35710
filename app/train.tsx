import {Button, StyleSheet, View} from 'react-native';

import {HelloWave} from '@/components/hello-wave';
import {ThemedText} from '@/components/themed-text';
import {ThemedView} from '@/components/themed-view';
import {SafeAreaView} from "react-native-safe-area-context";

export default function TrainScreen() {
    return (
        <SafeAreaView style={{padding: 16}}>
            <ThemedView style={styles.titleContainer}>
                <ThemedText type="title">Train</ThemedText>
                <HelloWave/>
            </ThemedView>
            <View>
                <Button title="Start"/>
            </View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    titleContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    stepContainer: {
        gap: 8,
        marginBottom: 8,
    },
    reactLogo: {
        height: 178,
        width: 290,
        bottom: 0,
        left: 0,
        position: 'absolute',
    },
});
