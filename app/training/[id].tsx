import {Button, StyleSheet, View} from 'react-native';
import {ThemedText} from '@/components/themed-text';
import {ThemedView} from '@/components/themed-view';
import {SafeAreaView} from "react-native-safe-area-context";
import {useLocalSearchParams} from "expo-router";
import {trainings} from "@/constants";
import {useEffect, useRef, useState} from "react";
import {ExerciseCard} from "@/components/exercises/ExerciseCard";
import {IconSymbol} from "@/components/ui/icon-symbol";
import {useToggle} from "@uidotdev/usehooks";
import {GestureHandlerRootView} from "react-native-gesture-handler";
import BottomSheet, {BottomSheetView} from "@gorhom/bottom-sheet";

const fetchExerciseById = async (id: string, exercise: any) => {
    return fetch(`https://exercisesdb-sand.vercel.app/api/v1/exercises/${id}`)
        .then((response) => response.json())
        .then((json) => ({
            ...json.data,
            ...exercise
        }))
}

export default function TrainingScreen() {
    const {id} = useLocalSearchParams();

    const bottomSheetRef = useRef<BottomSheet>(null);
    const [isEditMode, toggleEditMode] = useToggle(false)
    const [exercises, setExercises] = useState<any[]>([])

    const training = trainings.find((training) => training.id === id);

    if (!training) {
        return
    }

    useEffect(() => {
        if (training) {
            Promise.all(training.exercises.map(exercise => fetchExerciseById(exercise.id, exercise)))
                .then(results => setExercises(results))
        }
    }, []);

    useEffect(() => {
        if (isEditMode) {
            bottomSheetRef.current?.snapToIndex(0);
            return
        }
        bottomSheetRef.current?.close();
    }, [isEditMode]);

    return (
        <ThemedView style={{flex: 1}}>
            <SafeAreaView style={{padding: 16}}>
                <ThemedView>
                    <ThemedText type="title">{training!.name}</ThemedText>
                    <IconSymbol
                        size={32}
                        color="#808080"
                        name="rectangle.expand.diagonal"
                    />
                </ThemedView>
                <View>
                    <Button title="Start"/>
                    <Button title={isEditMode ? "Save" : "Edit"} onPress={() => toggleEditMode()}/>
                </View>

                {exercises.map((exercise) => {
                    return <ExerciseCard key={exercise.exerciseId} exercise={exercise}/>
                })}

                <GestureHandlerRootView style={styles.container}>
                    <BottomSheet
                        ref={bottomSheetRef}
                        snapPoints={[200, "100%"]}
                    >
                        <BottomSheetView style={styles.container}>
                            <ThemedText>Awesome 🎉</ThemedText>
                        </BottomSheetView>
                    </BottomSheet>
                </GestureHandlerRootView>
            </SafeAreaView>
        </ThemedView>
    );
}

const styles = StyleSheet.create({
    titleContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    stepContainer: {
        gap: 8,
        marginBottom: 8,
    },
    reactLogo: {
        height: 178,
        width: 290,
        bottom: 0,
        left: 0,
        position: 'absolute',
    },
    container: {
        flex: 1,
        backgroundColor: 'grey',
    },
    contentContainer: {
        flex: 1,
        padding: 36,
        alignItems: 'center',
    },
});
