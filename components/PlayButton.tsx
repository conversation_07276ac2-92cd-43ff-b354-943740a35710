import {Link} from "expo-router";
import {IconSymbol} from "@/components/ui/icon-symbol";
import {StyleSheet} from 'react-native';
import {ThemedText} from "@/components/themed-text";

export const PlayButton = () => {
    return <Link href="/train"
                 style={styles.button}>
        <ThemedText>
            Start
        </ThemedText>
        <IconSymbol name="chevron.right" color="red"/>
    </Link>
}

const styles = StyleSheet.create({
    button: {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        flexDirection: "row",
        height: 32,
        padding: 8,
        borderRadius: 8,
        backgroundColor: "lightblue",
        color: "white"
    }
});