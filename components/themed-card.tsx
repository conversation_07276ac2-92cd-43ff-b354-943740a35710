import {StyleSheet, View, type ViewProps} from 'react-native';

import { useThemeColor } from '@/hooks/use-theme-color';

export type ThemedViewProps = ViewProps & {
  lightColor?: string;
  darkColor?: string;
};

export function ThemedCard({ style, lightColor, darkColor, ...otherProps }: ThemedViewProps) {
  const backgroundColor = useThemeColor({ light: lightColor, dark: darkColor }, 'background');
  const cardStyle = StyleSheet.create({
    card : {
      color: "#FFF",
      borderColor: "#555",
      borderWidth: 1,
      borderStyle: "solid",
      padding: 8,
      borderRadius: 8
    }
  })

  return <View style={[{ backgroundColor }, cardStyle.card, style]} {...otherProps} />;
}
